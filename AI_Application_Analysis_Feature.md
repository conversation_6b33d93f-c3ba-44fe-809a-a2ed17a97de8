# AI Application Analysis Feature

## Overview
The AI Application Analysis feature has been added to the applicants create form to automatically pre-fill form fields by analyzing uploaded application documents.

## How It Works

### 1. File Upload
- Users can upload application files in the following formats:
  - TXT (Text files) - **Currently fully supported**
  - PDF (Portable Document Format) - *Requires additional libraries*
  - DOC/DOCX (Microsoft Word documents) - *Requires additional libraries*

### 2. AI Analysis Process
- The file is uploaded to the browser temporarily (not saved to the server)
- When the user clicks "Analyze with AI", the file is sent to the server for processing
- The system extracts text from the file and analyzes it using pattern matching
- Form fields are automatically pre-filled with extracted information

### 3. Supported Fields
The AI analysis can extract and pre-fill the following fields:

**Personal Information:**
- Name
- Gender/Sex
- Age
- Place of Origin
- Address/Location
- Contact Details (email, phone)
- NID Number

**Professional Information:**
- Current Employer
- Current Position
- Qualifications
- Other Trainings

**Skills and Experience:**
- Knowledge
- Skills and Competencies
- Job Experiences
- Publications

**Additional Information:**
- Awards
- Referees
- Comments (automatically adds analysis timestamp)

## Usage Instructions

### For Users:
1. Navigate to the applicants create form
2. Scroll down to the "AI Application Analysis" section
3. Click "Choose application file..." and select your application document
4. Click "Analyze with AI" button
5. Wait for the analysis to complete
6. Review and modify the pre-filled fields as needed
7. Submit the application form

### For Developers:

#### Files Modified:
- `app/Views/applicants/applicants_create.php` - Added AI upload section and JavaScript
- `app/Controllers/Applicants.php` - Added `analyzeApplication()` method and helper functions
- `app/Config/Routes.php` - Added route for AI analysis endpoint

#### Key Components:

**Controller Method:**
```php
public function analyzeApplication()
```
- Handles file upload and validation
- Extracts text from files
- Performs AI analysis using pattern matching
- Returns JSON response with extracted data

**JavaScript Functions:**
- File selection handling
- AJAX request to analysis endpoint
- Form field population
- User feedback (loading, success, error states)

**Text Extraction Methods:**
- `extractTextFromFile()` - Handles different file formats
- Multiple `extract*()` helper methods for specific field types

## Current Limitations

1. **File Format Support:**
   - Only TXT files are fully supported
   - PDF and DOC/DOCX require additional PHP libraries (TCPDF, PHPWord)

2. **AI Analysis:**
   - Currently uses pattern matching (regex) instead of true AI
   - For real AI integration, consider services like OpenAI, Google AI, or Azure Cognitive Services

3. **Text Extraction:**
   - Simple text patterns may not capture all variations
   - Complex document structures may not be parsed correctly

## Future Enhancements

1. **Enhanced File Support:**
   - Install and configure PDF text extraction libraries
   - Add support for DOC/DOCX files using PHPWord

2. **Real AI Integration:**
   - Integrate with OpenAI GPT API for better text understanding
   - Use natural language processing for more accurate extraction

3. **Improved Pattern Matching:**
   - Add more sophisticated regex patterns
   - Support multiple languages and formats

4. **User Experience:**
   - Add file preview functionality
   - Provide confidence scores for extracted data
   - Allow manual correction of extracted fields

## Testing

A sample application file has been created at `public/sample_application.txt` for testing purposes. This file contains properly formatted application data that the AI analysis can successfully extract.

## Security Considerations

- Files are processed temporarily and not permanently stored
- File type validation prevents malicious uploads
- Error handling prevents system crashes from invalid files
- User input is sanitized before database insertion

## Installation Notes

No additional dependencies are required for the basic TXT file functionality. For enhanced file format support, consider installing:

- For PDF: `composer require tecnickcom/tcpdf`
- For DOC/DOCX: `composer require phpoffice/phpword`
