<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><?= $title ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("applicants") ?>">Positions</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url("applicants/list/{$position_id}") ?>">Applicants</a></li>
                        <li class="breadcrumb-item active">Create</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <?= form_open('applicants/store/' . $position_id) ?>
            <div class="row">
                <div class="col-md-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user-plus mr-2"></i>
                                Personal Information
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="name">Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                           
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sex">Sex</label>
                                        <select class="form-control" id="sex" name="sex" required>
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="age">Age</label>
                                        <input type="number" class="form-control" id="age" name="age" required>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="place_origin">Place of Origin</label>
                                <input type="text" class="form-control" id="place_origin" name="place_origin">
                            </div>
                            <div class="form-group">
                                <label for="address_location">Address/Location</label>
                                <input type="text" class="form-control" id="address_location" name="address_location">
                            </div>
                            <div class="form-group">
                                <label for="contact_details">Contact Details <small>(e.g. Email, Phone Number)</small> </label>
                                <input type="text" class="form-control" id="contact_details" name="contact_details">
                            </div>
                            <div class="form-group">
                                <label for="nid_number">NID Number</label>
                                <input type="text" class="form-control" id="nid_number" name="nid_number">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card card-info">
                        <div class="card-header">
                            <h3 class="card-title">Professional Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="current_employer">Current Employer</label>
                                <input type="text" class="form-control" id="current_employer" name="current_employer">
                            </div>
                            <div class="form-group">
                                <label for="current_position">Current Position</label>
                                <input type="text" class="form-control" id="current_position" name="current_position">
                            </div>
                            <div class="form-group">
                                <label for="qualification_text"><strong>Qualifications:</strong></label>
                                <textarea class="form-control" id="qualification_text" name="qualification_text" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="other_trainings"><strong>Other Trainings:</strong></label>
                                <textarea class="form-control" id="other_trainings" name="other_trainings" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="card card-success">
                        <div class="card-header">
                            <h3 class="card-title">Skills and Experience</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="knowledge"><strong>Knowledge:</strong></label>
                                <textarea class="form-control" id="knowledge" name="knowledge" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="skills_competencies"><strong>Skills and Competencies:</strong></label>
                                <textarea class="form-control" id="skills_competencies" name="skills_competencies" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="job_experiences"><strong>Job Experiences:</strong></label>
                                <textarea class="form-control" id="job_experiences" name="job_experiences" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="publications"><strong>Publications:</strong></label>
                                <textarea class="form-control" id="publications" name="publications" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card card-warning">
                        <div class="card-header">
                            <h3 class="card-title">Additional Information</h3>
                        </div>
                        <div class="card-body">
                        
                           
                            <div class="form-group">
                                <label for="awards"><strong>Awards:</strong></label>
                                <textarea class="form-control" id="awards" name="awards" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="referees"><strong>Referees:</strong></label>
                                <textarea class="form-control" id="referees" name="referees" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="comments"><strong>Comments:</strong></label>
                                <textarea class="form-control" id="comments" name="comments" rows="3"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="is_active">Active</label>
                                <select class="form-control" id="is_active" name="is_active">
                                    <option value="1">Yes</option>
                                    <option value="0">No</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- AI Application File Upload Section -->
            <div class="row">
                <div class="col-12">
                    <div class="card card-secondary">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-robot mr-2"></i>
                                AI Application Analysis
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                Upload an application file (CV, resume, or application document) to automatically pre-fill the form fields using AI analysis.
                            </div>

                            <div class="form-group">
                                <label for="ai_file">Application File</label>
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="ai_file" accept=".pdf,.doc,.docx,.txt">
                                    <label class="custom-file-label" for="ai_file">Choose application file...</label>
                                </div>
                                <small class="form-text text-muted">Supported formats: PDF, DOC, DOCX, TXT</small>
                            </div>

                            <div class="form-group">
                                <button type="button" class="btn btn-info" id="analyze_btn" disabled>
                                    <i class="fas fa-magic mr-2"></i>
                                    Analyze with AI
                                </button>
                                <div id="analysis_loading" class="d-none">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    Analyzing file with AI...
                                </div>
                            </div>

                            <div id="analysis_result" class="alert alert-success d-none">
                                <i class="fas fa-check-circle mr-2"></i>
                                Analysis complete! Form fields have been pre-filled. Please review and modify as needed.
                            </div>

                            <div id="analysis_error" class="alert alert-danger d-none">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <span id="error_message">Error analyzing file. Please try again.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary btn-lg btn-block">Submit Application</button>
                </div>
            </div>
            <?= form_close() ?>
        </div>
    </section>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    let selectedFile = null;

    // Handle file selection
    $('#ai_file').on('change', function() {
        const file = this.files[0];
        if (file) {
            selectedFile = file;
            $('.custom-file-label').text(file.name);
            $('#analyze_btn').prop('disabled', false);

            // Hide previous results
            $('#analysis_result, #analysis_error').addClass('d-none');
        } else {
            selectedFile = null;
            $('.custom-file-label').text('Choose application file...');
            $('#analyze_btn').prop('disabled', true);
        }
    });

    // Handle AI analysis (JavaScript-only implementation)
    $('#analyze_btn').on('click', function() {
        if (!selectedFile) {
            return;
        }

        // Show loading state
        $('#analyze_btn').prop('disabled', true);
        $('#analysis_loading').removeClass('d-none');
        $('#analysis_result, #analysis_error').addClass('d-none');

        // Read file content using FileReader API
        const reader = new FileReader();

        reader.onload = function(e) {
            try {
                const fileContent = e.target.result;

                // Validate file type and content
                if (!validateFileContent(selectedFile, fileContent)) {
                    throw new Error('Invalid file format or content. Please upload a text-based application file.');
                }

                // Perform JavaScript-based AI analysis
                const analysisResult = analyzeApplicationText(fileContent);

                // Pre-fill form fields with analysis results
                fillFormFields(analysisResult);

                // Show success message
                $('#analysis_result').removeClass('d-none');

            } catch (error) {
                // Show error message
                $('#error_message').text(error.message || 'Error analyzing file. Please try again.');
                $('#analysis_error').removeClass('d-none');
            } finally {
                // Hide loading state
                $('#analysis_loading').addClass('d-none');
                $('#analyze_btn').prop('disabled', false);
            }
        };

        reader.onerror = function() {
            $('#error_message').text('Error reading file. Please try again.');
            $('#analysis_error').removeClass('d-none');
            $('#analysis_loading').addClass('d-none');
            $('#analyze_btn').prop('disabled', false);
        };

        // Read file as text
        reader.readAsText(selectedFile);
    });

    // Function to validate file content
    function validateFileContent(file, content) {
        // Check file size (max 25MB)
        if (file.size > 25 * 1024 * 1024) {
            throw new Error('File size too large. Please upload a file smaller than 25MB.');
        }

        // Check if content is readable text
        if (!content || content.trim().length < 50) {
            throw new Error('File content is too short or empty. Please upload a valid application document.');
        }

        // Check for common file types based on content
        const fileExtension = file.name.split('.').pop().toLowerCase();
        const allowedExtensions = ['txt', 'pdf', 'doc', 'docx'];

        if (!allowedExtensions.includes(fileExtension)) {
            throw new Error('Unsupported file type. Please upload TXT, PDF, DOC, or DOCX files.');
        }

        // For now, only support text files for full functionality
        if (fileExtension !== 'txt') {
            throw new Error('Currently only TXT files are fully supported. Please convert your document to TXT format.');
        }

        return true;
    }

    // JavaScript-based AI analysis function
    function analyzeApplicationText(text) {
        const result = {
            name: extractName(text),
            sex: extractGender(text),
            age: extractAge(text),
            place_origin: extractPlaceOfOrigin(text),
            address_location: extractAddress(text),
            contact_details: extractContactDetails(text),
            nid_number: extractNIDNumber(text),
            current_employer: extractCurrentEmployer(text),
            current_position: extractCurrentPosition(text),
            qualification_text: extractQualifications(text),
            other_trainings: extractTrainings(text),
            knowledge: extractKnowledge(text),
            skills_competencies: extractSkills(text),
            job_experiences: extractExperience(text),
            publications: extractPublications(text),
            awards: extractAwards(text),
            referees: extractReferees(text),
            comments: 'Analyzed by JavaScript AI on ' + new Date().toLocaleString()
        };

        return result;
    }

    // Text extraction helper functions
    function extractName(text) {
        // Look for patterns like "Name:", "Full Name:", etc.
        let match = text.match(/(?:name|full name|applicant name):\s*([^\n\r]+)/i);
        if (match) return match[1].trim();

        // Look for patterns at the beginning of the document
        const lines = text.split('\n');
        for (let line of lines) {
            line = line.trim();
            if (line.length > 5 && line.length < 50 && /^[A-Za-z\s]+$/.test(line)) {
                return line;
            }
        }

        return '';
    }

    function extractGender(text) {
        const match = text.match(/(?:gender|sex):\s*(male|female|m|f)/i);
        if (match) {
            const gender = match[1].toLowerCase();
            return (gender === 'm' || gender === 'male') ? 'Male' : 'Female';
        }
        return '';
    }

    function extractAge(text) {
        // Look for age pattern
        let match = text.match(/(?:age):\s*(\d{1,2})/i);
        if (match) return match[1];

        // Look for birth year and calculate age
        match = text.match(/(?:born|birth).*?(\d{4})/i);
        if (match) {
            const birthYear = parseInt(match[1]);
            const currentYear = new Date().getFullYear();
            if (birthYear > 1900 && birthYear < currentYear) {
                return currentYear - birthYear;
            }
        }

        return '';
    }

    function extractPlaceOfOrigin(text) {
        const match = text.match(/(?:place of origin|origin|birthplace):\s*([^\n\r]+)/i);
        return match ? match[1].trim() : '';
    }

    function extractAddress(text) {
        const match = text.match(/(?:address|location|residence):\s*([^\n\r]+)/i);
        return match ? match[1].trim() : '';
    }

    function extractContactDetails(text) {
        const contacts = [];

        // Extract email
        const emailMatch = text.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
        if (emailMatch) contacts.push(emailMatch[1]);

        // Extract phone numbers
        const phoneMatch = text.match(/(?:phone|mobile|tel|contact):\s*([+\d\s\-()]+)/i);
        if (phoneMatch) contacts.push(phoneMatch[1].trim());

        return contacts.join(', ');
    }

    function extractNIDNumber(text) {
        const match = text.match(/(?:nid|national id|id number):\s*(\w+)/i);
        return match ? match[1] : '';
    }

    function extractCurrentEmployer(text) {
        const match = text.match(/(?:current employer|employer|company):\s*([^\n\r]+)/i);
        return match ? match[1].trim() : '';
    }

    function extractCurrentPosition(text) {
        const match = text.match(/(?:current position|position|job title|designation):\s*([^\n\r]+)/i);
        return match ? match[1].trim() : '';
    }

    function extractQualifications(text) {
        const qualifications = [];

        // Look for education section
        const educationMatch = text.match(/(?:education|qualifications?|academic)[:\s]*([^]*?)(?:\n\n|\r\r|(?:OTHER|SKILLS|EXPERIENCE|KNOWLEDGE))/i);
        if (educationMatch) {
            qualifications.push(educationMatch[1].trim());
        }

        // Look for degree patterns
        const degreeMatches = text.match(/(?:bachelor|master|phd|diploma|certificate|degree).*?(?:in|of)\s+([^\n\r]+)/gi);
        if (degreeMatches) {
            qualifications.push(...degreeMatches);
        }

        return [...new Set(qualifications)].join('\n');
    }

    function extractTrainings(text) {
        const match = text.match(/(?:training|courses?|certification)[:\s]*([^]*?)(?:\n\n|\r\r|(?:KNOWLEDGE|SKILLS|EXPERIENCE))/i);
        return match ? match[1].trim() : '';
    }

    function extractKnowledge(text) {
        const match = text.match(/(?:knowledge|expertise|specialization)[:\s]*([^]*?)(?:\n\n|\r\r|(?:SKILLS|EXPERIENCE|JOB))/i);
        return match ? match[1].trim() : '';
    }

    function extractSkills(text) {
        const match = text.match(/(?:skills?|competenc|abilities)[:\s]*([^]*?)(?:\n\n|\r\r|(?:EXPERIENCE|JOB|KNOWLEDGE))/i);
        return match ? match[1].trim() : '';
    }

    function extractExperience(text) {
        const match = text.match(/(?:experience|work history|employment|job experiences?)[:\s]*([^]*?)(?:\n\n|\r\r|(?:PUBLICATIONS|AWARDS|REFEREES))/i);
        return match ? match[1].trim() : '';
    }

    function extractPublications(text) {
        const match = text.match(/(?:publications?|research|papers?)[:\s]*([^]*?)(?:\n\n|\r\r|(?:AWARDS|REFEREES|ADDITIONAL))/i);
        return match ? match[1].trim() : '';
    }

    function extractAwards(text) {
        const match = text.match(/(?:awards?|honors?|achievements?)[:\s]*([^]*?)(?:\n\n|\r\r|(?:REFEREES|ADDITIONAL|COMMENTS))/i);
        return match ? match[1].trim() : '';
    }

    function extractReferees(text) {
        const match = text.match(/(?:referees?|references?)[:\s]*([^]*?)(?:\n\n|\r\r|(?:ADDITIONAL|COMMENTS))/i);
        return match ? match[1].trim() : '';
    }

    // Function to fill form fields with AI analysis data
    function fillFormFields(data) {
        // Personal Information
        if (data.name) $('#name').val(data.name);
        if (data.sex) $('#sex').val(data.sex);
        if (data.age) $('#age').val(data.age);
        if (data.place_origin) $('#place_origin').val(data.place_origin);
        if (data.address_location) $('#address_location').val(data.address_location);
        if (data.contact_details) $('#contact_details').val(data.contact_details);
        if (data.nid_number) $('#nid_number').val(data.nid_number);

        // Professional Information
        if (data.current_employer) $('#current_employer').val(data.current_employer);
        if (data.current_position) $('#current_position').val(data.current_position);
        if (data.qualification_text) $('#qualification_text').val(data.qualification_text);
        if (data.other_trainings) $('#other_trainings').val(data.other_trainings);

        // Skills and Experience
        if (data.knowledge) $('#knowledge').val(data.knowledge);
        if (data.skills_competencies) $('#skills_competencies').val(data.skills_competencies);
        if (data.job_experiences) $('#job_experiences').val(data.job_experiences);
        if (data.publications) $('#publications').val(data.publications);

        // Additional Information
        if (data.awards) $('#awards').val(data.awards);
        if (data.referees) $('#referees').val(data.referees);
        if (data.comments) $('#comments').val(data.comments);
    }
});
</script>
<?= $this->endSection() ?>
